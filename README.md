# LiftLoom

<p align="center">
  <img src="resources/icon.png" alt="LiftLoom Logo" width="200" />
</p>

<p align="center">
  一款高度可定制的 LLM 聊天客户端应用
</p>

## 📖 项目概述

LiftLoom 是一款基于 Electron 技术栈开发的高度可定制 LLM 聊天客户端应用，旨在提供灵活且智能的 AI 交互体验。该应用通过 MCP 插件系统和模块化设计，使用户能够根据自身需求定制和扩展应用功能。

## 🚀 特性

- 🔌 **强大的插件系统**：基于 MCP (Module Control Protocol) 架构，支持功能扩展
- 🎨 **可定制界面风格**：风格参考 Linear App
- 🤖 **多模型支持**：接入各种 LLM 模型 (OpenAI, Anthropic, 本地模型等)
- 💾 **本地数据存储**：对话历史和配置的本地持久化
- 🌐 **联网能力**：灵活的 API 调用和资源访问
- 🔒 **安全保障**：插件沙箱和 API 密钥加密存储

## 🛠️ 技术栈

- **基础框架**：Electron + React + TypeScript
- **API 通信**：REST API, Stream, WebSocket
- **数据存储**：SQLite + drizzleORM
- **UI 组件库**：Tailwind CSS + shadcn
- **插件系统**：MCP 架构

## 💻 核心功能模块

### 1. 用户界面 (UI)

- 可配置界面
- 风格参考 Linear App
- 支持主题切换（明/暗模式）
- 设计对话气泡、输入区域和工具栏

### 2. 聊天引擎

- 支持多种 LLM 模型接入
- 实现对话上下文管理
- 提供流式响应处理
- 支持富文本和 Markdown 渲染

### 3. 插件系统 (MCP)

- 实现插件加载/卸载机制
- 提供安全的插件沙箱环境
- 定义标准化的插件 API
- 支持本地插件管理

### 4. 数据管理

- 实现对话历史存储和检索
- 支持对话导出/导入
- 提供用户配置持久化

### 5. 联网和资源访问

- 实现 API 密钥管理
- 提供代理设置
- 支持文件上传/下载

### 6. 辅助功能

- 提供快捷键支持
- 提供自动更新机制

## 🧩 插件开发规范

1. 插件必须遵循 MCP 协议标准
2. 每个插件需要明确声明所需权限
3. 插件应实现生命周期钩子函数
4. 提供详细的文档和使用示例

## 🔐 安全考虑

1. 实现沙箱机制隔离插件运行环境
2. 加密存储用户 API 密钥
3. 采用签名验证插件完整性
4. 提供权限控制系统

## ⚡ 性能优化

1. 实现虚拟滚动加载长对话
2. 使用 Web Worker 处理复杂计算
3. 采用懒加载优化资源使用
4. 实现智能缓存减少网络请求

## 📝 主要功能点

- 基于 Electron + React + TypeScript 构建
- 可定制的用户界面 (UI) 和基本交互
- LLM API 接口集成
- MCP 插件系统核心
- SQLite 和 DrizzleORM 实现数据持久化
- 应用打包和自动更新机制

## 🔮 项目愿景

LiftLoom 不仅是一个简单的聊天客户端，而是一个能够随用户需求不断成长的智能助手平台。通过灵活的插件系统和高度可定制的界面，用户可以打造专属的 AI 交互体验，提升工作效率和创造力。

## 🚦 开始使用

## 💻 项目结构
```
.
├──src
│  ├──main
│  │  ├──index.ts
│  │  └──db.ts
│  ├──preload
│  │  ├──index.ts
│  │  └──index.d.ts
│  └──renderer    # with React, entry files: App.tsx, main.tsx
│     ├──src
│     │  ├──assets/ (contains various asset files)
│     │  ├──components/ (contains various component directories and files)
│     │  ├──config/ (contains config files)
│     │  ├──hooks/ (contains hook files)
│     │  ├──lib/ (contains utility files)
│     │  ├──providers/ (contains provider files)
│     │  ├──service/ (contains service files)
│     │  ├──types/ (contains type definition files)
│     │  ├──App.tsx
│     │  ├──env.d.ts
│     │  ├──main.tsx
│     ├──index.html
├──electron.vite.config.ts
├──package.json
├──README.md
├──vite.config.ts
├──tsconfig.json
├──tsconfig.node.json
├──tsconfig.web.json
(and other configuration files like .gitignore, .prettierrc.yaml, etc.)
```

## 📄 许可证

[MIT License](LICENSE)