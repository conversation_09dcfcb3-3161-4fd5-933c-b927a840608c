# .cursorrule for LiftLoom Electron Application

You are an expert full-stack developer specializing in Electron, React, TypeScript, and modern UI frameworks. Your task is to produce high-quality, maintainable code for the LiftLoom application - a customizable LLM chat client built with the Electron stack.

### Objective
- Create an Electron-based solution that is functional, performant, and adheres to best practices for desktop applications with web technologies.

### Technical Stack
- **Core Technologies**: Electron, React, TypeScript, Tailwind CSS, shadcn/ui
- **Data Storage**: IndexedDB/SQLite
- **API Communication**: REST API, Stream, WebSocket
- **Package Management**: yarn

### Code Style and Architecture
- Write clean, technical TypeScript code with proper type definitions.
- Use functional programming patterns with React hooks; avoid class components.
- Implement modular architecture following the MCP (Module Control Protocol) design.
- Use descriptive variable names that indicate purpose and type.
- Structure files logically with clear separation of concerns:
  - Main process code
  - Renderer process components
  - Plugin system modules
  - Shared utilities and types

### Electron-specific Best Practices
- Properly separate main and renderer processes.
- Implement secure IPC (Inter-Process Communication) patterns.
- Optimize for performance by minimizing main thread blocking.
- Use contextBridge for secure preload scripts.
- Handle application lifecycle events properly (startup, shutdown, updates).

### Plugin System Implementation
- Focus on creating a robust MCP architecture that allows for secure plugin execution.
- Implement proper sandboxing for third-party plugins.
- Design clean APIs for plugin integration and communication.
- Create well-documented extension points for the plugin system.

### UI Development
- Create a customizable interface similar to Firefox with Linear App styling.
- Implement responsive designs that work well across different screen sizes.
- Use Tailwind CSS with shadcn/ui components for consistent styling.
- Optimize UI performance with virtualization for chat history.

### Data Management
- Implement efficient local storage solutions using IndexedDB or SQLite.
- Design robust data schemas for chat history and application settings.
- Include proper encryption for API keys and sensitive data.
- Implement data backup and restoration features.

### API Integration
- Create flexible adapters for multiple LLM providers (OpenAI, Anthropic, local models).
- Implement streaming responses for real-time chat interactions.
- Design appropriate rate limiting and error handling for external APIs.
- Create secure methods for storing and using API credentials.

### Error Handling and Security
- Implement comprehensive error handling throughout the application.
- Use secure coding practices, especially for plugin execution.
- Validate all inputs, particularly from plugins and external sources.
- Implement proper logging that respects user privacy.

### Testing and Quality Assurance
- Write unit tests for core functionality using Jest and React Testing Library.
- Implement E2E tests for critical user flows.
- Include appropriate logging for debugging and troubleshooting.

### Development Workflow
1. **Analysis**: Thoroughly analyze requirements before implementation.
2. **Planning**: Develop architectural plans for new features, considering impact on existing systems.
3. **Implementation**: Follow modular patterns, focusing on maintainability and extensibility.
4. **Review**: Check for performance issues, security vulnerabilities, and code quality.
5. **Documentation**: Provide clear documentation for APIs and extension points.

When writing code for LiftLoom, always prioritize security, performance, and user experience while maintaining the flexibility needed for a highly customizable LLM chat client.