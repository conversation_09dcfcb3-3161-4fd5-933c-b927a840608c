import { ElectronAPI } from '@electron-toolkit/preload'

// 定义ProviderType（与渲染进程保持一致）
type ProviderType = 'openai' | 'anthropic' | 'gemini' | 'qwenlm' | 'azure-openai' | 'deepseek'

// 定义Provider类型（与渲染进程保持一致）
interface Provider {
  id: string
  type: ProviderType
  name: string
  apiKey: string
  apiHost: string
  models: Array<{
    id: string
    provider: string
    name: string
    group: string
    description?: string
  }>
  enabled: boolean
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      // Provider相关API
      getAllProviders: () => Promise<Provider[]>
      getProvider: (id: string) => Promise<Provider | null>
      saveProvider: (providerData: any) => Promise<number>
      deleteProvider: (id: string) => Promise<boolean>

      // Model相关API
      getAllModels: () => Promise<Array<{
        id: string
        provider: string
        name: string
        group: string
        description?: string
      }>>
      getModelSettings: (modelId: string) => Promise<any | null>
      saveModelSettings: (modelData: any) => Promise<boolean>
      deleteModel: (modelId: string) => Promise<boolean>

      // Conversation相关API
      getAllConversations: () => Promise<Array<{
        id: number
        title: string
        createdAt: string
      }>>
      createConversation: (title: string) => Promise<number>
      addMessage: (conversationId: number, role: string, content: string, modelId: number) => Promise<number>
      getMessagesByConversation: (conversationId: number) => Promise<Array<{
        id: number
        conversationId: number
        role: string
        content: string
        modelId: number
        createdAt: string
      }>>
      clearAllHistory: () => Promise<boolean>
    }
  }
}
