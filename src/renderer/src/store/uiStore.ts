import { create } from 'zustand';
import { useChatStore } from './chatStore';

// UI 状态接口
interface UIState {
  /* ---- 全局界面状态 ---- */
  colorTheme: string;
  isModelConfigPanelOpen: boolean;

  /* ===== ACTIONS ===== */
  openModelConfigPanel: () => void;
  closeModelConfigPanel: () => void;
  toggleModelConfigPanel: () => void;
  setColorTheme: (theme: string) => void;
}

// 创建 UI Store
export const useUIStore = create<UIState>((set, get) => ({
  // 初始状态
  colorTheme: "11",
  isModelConfigPanelOpen: false,

  // Actions
  openModelConfigPanel: () => {
    set({ isModelConfigPanelOpen: true });

    // 当打开模型配置面板时，清空聊天状态
    useChatStore.getState().deselectConversation();
  },

  closeModelConfigPanel: () => {
    set({ isModelConfigPanelOpen: false });
  },

  toggleModelConfigPanel: () => {
    const { isModelConfigPanelOpen } = get();
    const opening = !isModelConfigPanelOpen;

    set({ isModelConfigPanelOpen: opening });

    // 如果是打开面板，需要清空聊天状态
    if (opening) {
      useChatStore.getState().deselectConversation();
    }
  },

  setColorTheme: (theme: string) => {
    set({ colorTheme: theme });
  },
}));
