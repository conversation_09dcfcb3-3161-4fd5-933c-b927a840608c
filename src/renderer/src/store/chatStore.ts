import { formatTime } from '@renderer/lib/dateUtil';
import {
    clearAllHistory as dbClearAllHistory,
    getAllConversations as dbGetAllConversations,
    getMessagesByConversationId as dbGetMessagesByConversationId
} from '@renderer/service/conversationService';
import { create } from 'zustand';

// 会话类型定义
export interface Conversation {
  id: number;
  title: string;
  time: string;
  selected: boolean;
}

// 消息类型定义
export interface Message {
  id: number;
  text: string;
  isUser: boolean;
}

// 聊天状态接口
interface ChatState {
  /* ---- 侧边栏界面状态 ---- */
  conversations: Conversation[];
  isLoadingConversations: boolean;
  selectedConversationId: number | null;

  /* ---- 聊天界面状态 ---- */
  messages: Message[];
  isLoadingMessages: boolean;
  isStreamingAIMessage: boolean;
  inputText: string;
  currentStreamingMessageId: number | null;

  /* ===== ACTIONS ===== */
  loadConversations: () => Promise<void>;
  selectConversation: (id: number | null) => Promise<void>;
  deselectConversation: () => void; // 新增：专门用于取消选中会话
  createNewConversationUI: () => void;
  clearHistory: () => Promise<void>;
  setInputText: (text: string) => void;
  sendMessage: (currentModelId: number) => Promise<void>;

  // 内部状态更新方法（供 service 调用）
  setStreamingState: (isStreaming: boolean) => void;
  setCurrentStreamingMessageId: (id: number | null) => void;
  updateStreamingMessage: (messageId: number, text: string) => void;
  addMessage: (message: Message) => void;
  updateMessageId: (oldId: number, newId: number) => void;
  removeMessage: (messageId: number) => void;
  handleSendError: (error: any) => void;
}

// 创建聊天 Store
export const useChatStore = create<ChatState>((set, get) => ({
  // 初始状态
  conversations: [],
  isLoadingConversations: false,
  selectedConversationId: null,
  messages: [],
  isLoadingMessages: false,
  isStreamingAIMessage: false,
  inputText: '',
  currentStreamingMessageId: null,

  // Actions
  loadConversations: async () => {
    set({ isLoadingConversations: true });
    try {
      const dbConversations = await dbGetAllConversations();
      const formattedConversations: Conversation[] = dbConversations.map(conv => ({
        id: conv.id,
        title: conv.title,
        time: formatTime(conv.createdAt),
        selected: get().selectedConversationId === conv.id,
      }));
      set({ conversations: formattedConversations, isLoadingConversations: false });
    } catch (error) {
      console.error('从chatStore加载会话失败:', error);
      set({ isLoadingConversations: false });
    }
  },

  selectConversation: async (id: number | null) => {
    if (get().isStreamingAIMessage) return; // 如果AI正在回复，则不允许切换

    if (id === null) {
      get().deselectConversation();
      return;
    }

    set(state => ({
      selectedConversationId: id,
      conversations: state.conversations.map(conv => ({
        ...conv,
        selected: conv.id === id,
      })),
      isLoadingMessages: true,
      messages: [], // 清空旧消息
      inputText: '',
    }));

    try {
      const dbMessages = await dbGetMessagesByConversationId(id);
      const formattedMessages: Message[] = dbMessages.map(msg => ({
        id: msg.id,
        text: msg.content,
        isUser: msg.role === 'user',
      }));
      set({ messages: formattedMessages, isLoadingMessages: false });
    } catch (error) {
      console.error(`从chatStore加载会话 ${id} 的消息失败:`, error);
      set({ isLoadingMessages: false, messages: [] }); // 出错时清空消息
    }
  },

  deselectConversation: () => {
    set(state => ({
      selectedConversationId: null,
      messages: [], // 清空消息
      conversations: state.conversations.map(conv => ({ ...conv, selected: false })),
      inputText: '', // 清空输入
    }));
  },

  createNewConversationUI: () => {
    if (get().isStreamingAIMessage) return;
    set(state => ({
      selectedConversationId: null,
      messages: [],
      conversations: state.conversations.map(conv => ({ ...conv, selected: false })),
      inputText: ''
    }));
  },

  clearHistory: async () => {
    if (get().isStreamingAIMessage) return;
    set({ isLoadingConversations: true });
    try {
      await dbClearAllHistory();
      set({
        conversations: [],
        selectedConversationId: null,
        messages: [],
        isLoadingConversations: false,
        inputText: '',
      });
    } catch (error) {
      console.error('从chatStore清空历史记录失败:', error);
      set({ isLoadingConversations: false });
    }
  },

  setInputText: (text: string) => set({ inputText: text }),

  // 简化的 sendMessage，具体逻辑已移到 chatService
  sendMessage: async (currentModelId: number) => {
    const { inputText, isStreamingAIMessage } = get();
    if (!inputText.trim() || isStreamingAIMessage) return;

    // 开始发送，设置流式状态
    set({ isStreamingAIMessage: true });

    // 将业务逻辑委托给 chatService
    const { chatService } = await import('@renderer/service/chatService');
    await chatService.sendMessage(inputText, currentModelId);
  },

  // 内部状态更新方法（供 service 调用）
  setStreamingState: (isStreaming: boolean) => {
    set({ isStreamingAIMessage: isStreaming });
  },

  setCurrentStreamingMessageId: (id: number | null) => {
    set({ currentStreamingMessageId: id });
  },

  updateStreamingMessage: (messageId: number, text: string) => {
    set(state => ({
      messages: state.messages.map(msg =>
        msg.id === messageId ? { ...msg, text } : msg
      ),
    }));
  },

  addMessage: (message: Message) => {
    set(state => ({
      messages: [...state.messages, message],
    }));
  },

  updateMessageId: (oldId: number, newId: number) => {
    set(state => ({
      messages: state.messages.map(msg =>
        msg.id === oldId ? { ...msg, id: newId } : msg
      ),
    }));
  },

  removeMessage: (messageId: number) => {
    set(state => ({
      messages: state.messages.filter(msg => msg.id !== messageId),
    }));
  },

  handleSendError: (error: any) => {
    console.error('发送消息错误:', error);
    set({ isStreamingAIMessage: false, currentStreamingMessageId: null });
    // TODO: 可以添加错误提示逻辑
  },
}));
