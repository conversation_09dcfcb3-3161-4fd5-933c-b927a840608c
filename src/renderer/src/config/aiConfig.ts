// AI配置
export interface AIConfig {
  provider: string;
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens?: number;
  systemPrompt?: string;
}

// 默认系统提示词
export const DEFAULT_SYSTEM_PROMPT = "你是一个有用的AI助手，会用中文回答用户的问题。";

// 从localStorage获取或设置配置
export function getAIConfig(): AIConfig {
  const storedConfig = localStorage.getItem('aiConfig');
  if (storedConfig) {
    return JSON.parse(storedConfig);
  }

  return {
    provider: 'openai',
    apiKey: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    systemPrompt: DEFAULT_SYSTEM_PROMPT
  };
}

export function saveAIConfig(config: AIConfig): void {
  localStorage.setItem('aiConfig', JSON.stringify(config));
}

// 获取系统提示词
export function getSystemPrompt(): string {
  const config = getAIConfig();
  return config.systemPrompt || DEFAULT_SYSTEM_PROMPT;
}