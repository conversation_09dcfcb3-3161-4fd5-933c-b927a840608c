import { ProviderType } from '@renderer/types';

// 定义供应商显示名称映射
export const PROVIDER_NAMES: Record<ProviderType, string> = {
  'openai': 'OpenAI',
  'anthropic': 'Anthropic',
  'gemini': 'Google Gemini',
  'qwenlm': 'Qwen',
  'azure-openai': 'Azure OpenAI',
  'deepseek': 'DeepSeek',
};

// 定义供应商默认API主机
export const DEFAULT_API_HOSTS: Record<ProviderType, string> = {
  'openai': 'https://api.openai.com/v1',
  'anthropic': 'https://api.anthropic.com',
  'gemini': 'https://generativelanguage.googleapis.com',
  'qwenlm': 'https://dashscope.aliyuncs.com/api/v1',
  'azure-openai': 'https://YOUR_RESOURCE_NAME.openai.azure.com',
  'deepseek': 'https://api.deepseek.com',
};

// 定义供应商描述
export const PROVIDER_DESCRIPTIONS: Record<ProviderType, string> = {
  'openai': '支持GPT-4o、GPT-4、o1等模型',
  'anthropic': '支持Claude 3.5 Sonnet、Claude 3系列模型',
  'gemini': '支持Gemini 1.5和2.0系列模型',
  'qwenlm': '支持通义千问系列模型',
  'azure-openai': '支持Azure部署的OpenAI模型',
  'deepseek': '支持DeepSeek系列模型',
};

// 定义供应商颜色主题
export const PROVIDER_COLORS: Record<ProviderType, string> = {
  'openai': 'bg-green-500',
  'anthropic': 'bg-purple-500',
  'gemini': 'bg-blue-500',
  'qwenlm': 'bg-yellow-500',
  'azure-openai': 'bg-blue-400',
  'deepseek': 'bg-indigo-500',
};

// 定义供应商图标文字
export const PROVIDER_ICON_TEXT: Record<ProviderType, string> = {
  'openai': 'AI',
  'anthropic': 'AN',
  'gemini': 'GM',
  'qwenlm': 'QW',
  'azure-openai': 'AZ',
  'deepseek': 'DS',
};

// 所有支持的供应商类型列表
export const ALL_PROVIDER_TYPES: ProviderType[] = [
  'openai',
  'anthropic', 
  'gemini',
  'deepseek',
  'qwenlm',
  'azure-openai'
];
