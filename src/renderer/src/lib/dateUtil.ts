
// 工具函数：格式化日期为ISO字符串

export function getISODate(): string {
  return new Date().toISOString();
}// 格式化日期显示

export function formatTime(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);

  // 如果是今天
  if (date.toDateString() === now.toDateString()) {
    return '今天';
  }

  // 如果是昨天
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天';
  }

  // 如果是一周内
  const oneWeekAgo = new Date(now);
  oneWeekAgo.setDate(now.getDate() - 7);
  if (date > oneWeekAgo) {
    return '本周';
  }

  // 一周前
  return '上周';
}