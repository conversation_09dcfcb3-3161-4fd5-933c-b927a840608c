import { PaperclipIcon } from "lucide-react";
import React, { useRef } from 'react';
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { ModelSelector } from "./ModelSelector";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  disabled?: boolean;
}

export function ChatInput({ value, onChange, onSend, disabled = false }: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend();
    }
  };

  return (
    <div className="p-4 w-full bg-[#f9f9f9] dark:bg-[#141414]">
      <div className="w-full px-4 sm:px-6 md:px-8">
        <div className="rounded-2xl border border-[#e5e5e5] dark:border-[#2A2A2A] overflow-hidden bg-background dark:bg-[#1c1c1c] shadow-sm">
          <Textarea
            ref={textareaRef}
            className="resize-none bg-transparent border-0 min-h-[100px] p-4 font-medium text-sm focus-visible:ring-0 w-full"
            placeholder="输入消息..."
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={disabled}
          />
          <div className="flex items-center justify-between p-2 bg-background/50 dark:bg-[#1c1c1c]/50">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full hover:bg-accent"
                disabled={disabled}
              >
                <PaperclipIcon className="h-3.5 w-3.5 text-muted-foreground" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <ModelSelector disabled={disabled} />
              <Button
                className="h-6 rounded-full bg-primary hover:bg-primary/90 px-3 text-xs"
                onClick={onSend}
                disabled={!value.trim() || disabled}
              >
                发送
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-2 text-xs text-center text-muted-foreground">
        LiftLoom 使用先进的AI模型提供回答，但可能不总是准确。
      </div>
    </div>
  );
}