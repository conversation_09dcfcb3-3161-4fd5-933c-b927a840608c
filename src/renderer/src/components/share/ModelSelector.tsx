import { useEffect } from 'react';
import { ChevronDown, Loader2 } from 'lucide-react';
import { useModelStore } from '@renderer/store/modelStore';
import { useProviderStore } from '@renderer/store/providerStore';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@renderer/components/ui/select';

interface ModelSelectorProps {
  disabled?: boolean;
  className?: string;
}

export function ModelSelector({ disabled = false, className }: ModelSelectorProps) {
  const {
    selectedModel,
    availableModels,
    isLoading,
    loadAvailableModels,
    selectModel,
    getSelectedModelDisplayName
  } = useModelStore();

  const { providers, loadProviders } = useProviderStore();

  // 初始化时加载数据
  useEffect(() => {
    const initializeData = async () => {
      // 先加载Provider数据
      await loadProviders();
      // 然后加载可用模型
      await loadAvailableModels();
    };

    initializeData();
  }, [loadProviders, loadAvailableModels]);

  // 当Provider数据变化时重新加载模型
  useEffect(() => {
    if (providers.length > 0) {
      loadAvailableModels();
    }
  }, [providers, loadAvailableModels]);

  const handleModelSelect = (value: string) => {
    const modelId = parseInt(value);
    if (!isNaN(modelId)) {
      selectModel(modelId);
    }
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 px-3 py-1.5 text-sm text-muted-foreground ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>加载模型...</span>
      </div>
    );
  }

  // 如果没有可用模型，显示提示
  if (availableModels.length === 0) {
    return (
      <div className={`flex items-center gap-2 px-3 py-1.5 text-sm text-muted-foreground ${className}`}>
        <span>无可用模型</span>
      </div>
    );
  }

  return (
    <Select
      value={selectedModel?.modelId.toString() || ''}
      onValueChange={handleModelSelect}
      disabled={disabled}
    >
      <SelectTrigger className={`w-auto min-w-[160px] h-8 text-xs border-0 bg-transparent hover:bg-accent/50 focus:ring-0 focus:ring-offset-0 ${className}`}>
        <SelectValue placeholder="选择模型">
          <span className="truncate">{getSelectedModelDisplayName()}</span>
        </SelectValue>
      </SelectTrigger>
      <SelectContent align="end" className="min-w-[200px]">
        {/* 按Provider分组显示模型 */}
        {Object.entries(groupModelsByProvider(availableModels)).map(([providerName, models]) => (
          <div key={providerName}>
            {/* Provider分组标题 */}
            <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
              {providerName}
            </div>
            {/* 该Provider下的模型列表 */}
            {models.map((model) => (
              <SelectItem
                key={model.modelId}
                value={model.modelId.toString()}
                className="pl-4"
              >
                <div className="flex flex-col">
                  <span className="font-medium">{model.modelName}</span>
                  <span className="text-xs text-muted-foreground">{model.modelStringId}</span>
                </div>
              </SelectItem>
            ))}
          </div>
        ))}
      </SelectContent>
    </Select>
  );
}

// 按Provider分组模型的辅助函数
function groupModelsByProvider(models: Array<{
  modelId: number;
  providerId: string;
  providerName: string;
  modelName: string;
  modelStringId: string;
  displayName: string;
}>) {
  return models.reduce((groups, model) => {
    const providerName = model.providerName;
    if (!groups[providerName]) {
      groups[providerName] = [];
    }
    groups[providerName].push(model);
    return groups;
  }, {} as Record<string, typeof models>);
}
