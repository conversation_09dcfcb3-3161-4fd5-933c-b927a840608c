import { useTheme } from "@renderer/components/theme-provider"
import { Toggle } from "@renderer/components/ui/toggle"
import { Moon, Sun } from "lucide-react"


export function ModeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <Toggle
      size="sm"
      className="group w-9 hover:text-accent-foreground"
      pressed={theme === "dark"}
      onPressedChange={() => {
        setTheme((theme === "dark") ? "light" : "dark");
      }}
      aria-label={`Switch to ${(theme === "dark") ? "light" : "dark"} mode`}
    >
      <Moon
        size={12}
        strokeWidth={2}
        className="shrink-0 scale-0 opacity-0 transition-all group-data-[state=on]:scale-100 group-data-[state=on]:opacity-100"
        aria-hidden="true"
      />
      <Sun
        size={12}
        strokeWidth={2}
        className="absolute shrink-0 scale-100 opacity-100 transition-all group-data-[state=on]:scale-0 group-data-[state=on]:opacity-0"
        aria-hidden="true"
      />
      <span className="sr-only">Toggle theme</span>
    </Toggle>
  )
}
