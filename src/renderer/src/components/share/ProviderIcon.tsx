import React from 'react';
import { ProviderType } from '@renderer/types';
import { PROVIDER_COLORS, PROVIDER_ICON_TEXT } from '@renderer/config/providers';
import { cn } from '@renderer/lib/utils';

interface ProviderIconProps {
  type: ProviderType;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeClasses = {
  sm: 'w-5 h-5 text-xs',
  md: 'w-6 h-6 text-xs',
  lg: 'w-8 h-8 text-sm'
};

export const ProviderIcon: React.FC<ProviderIconProps> = ({ 
  type, 
  size = 'md', 
  className 
}) => {
  const colorClass = PROVIDER_COLORS[type];
  const iconText = PROVIDER_ICON_TEXT[type];
  const sizeClass = sizeClasses[size];

  return (
    <div 
      className={cn(
        'rounded-full flex items-center justify-center text-white font-bold shadow-sm',
        colorClass,
        sizeClass,
        className
      )}
    >
      {iconText}
    </div>
  );
};
