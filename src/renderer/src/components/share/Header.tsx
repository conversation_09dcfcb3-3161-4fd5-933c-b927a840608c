import { Separator } from "../ui/separator";
import { SidebarTrigger } from "../ui/sidebar";
import { ModeToggle } from "./ModeToggle";

interface HeaderProps {
  title?: string;
}

export function Header({
  title = "LiftLoom",
}: HeaderProps) {
  return (
    <header className="border-b border-[#e5e5e5] dark:border-[#2A2A2A] h-10 px-4 flex justify-between items-center bg-white dark:bg-[#141414] z-10 shadow-sm">
      <div className="flex items-center gap-2">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <span className="font-semibold text-sm">{title}</span>
      </div>
      <div className="flex items-center gap-2">
        <ModeToggle />
      </div>
    </header>
  );
}