import { useProviderStore } from '@renderer/store/providerStore';
import { LoaderIcon, SettingsIcon } from 'lucide-react';
import React, { useEffect } from 'react';
import { ProviderConfigItem } from './ProviderConfigItem';

// 主组件
const ModelConfigPanel: React.FC = () => {
  // Store状态和操作
  const { providers, isLoading, loadProviders } = useProviderStore();

  // 初始加载
  useEffect(() => {
    loadProviders();
  }, [loadProviders]);

  if (isLoading) {
    return (
      <div className="h-full flex flex-col overflow-hidden">
        <div className="flex-1 flex justify-center items-center">
          <LoaderIcon className="w-6 h-6 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden bg-background">
      {/* 固定头部 */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto p-4 max-w-3xl">
          <div className="flex items-center gap-3">
            <SettingsIcon className="w-6 h-6 text-primary" />
            <h1 className="text-2xl font-bold">AI模型配置</h1>
          </div>
          <p className="text-muted-foreground mt-2">
            配置各种AI模型供应商的API密钥和设置，启用或禁用不同的供应商。测试连接以确保配置正确。
          </p>
        </div>
      </div>

      {/* 可滚动内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-4 max-w-3xl">
          <div className="space-y-4 pb-8">
            {providers.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <p>正在加载供应商配置...</p>
              </div>
            ) : (
              providers.map(provider => (
                <ProviderConfigItem
                  key={provider.id}
                  provider={provider}
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModelConfigPanel;
