import { ConversationList } from "@renderer/components/share/ConversationList";
import { SidebarSearch } from "@renderer/components/share/SidebarSearch";
import { ToolsMenu } from "@renderer/components/share/ToolsMenu";
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from "@renderer/components/ui/sidebar";
import { useChatStore } from "@renderer/store/chatStore";
import { useUIStore } from "@renderer/store/uiStore";
import { useEffect } from "react";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const conversations = useChatStore((state) => state.conversations);
  const isLoadingConversations = useChatStore((state) => state.isLoadingConversations);
  const selectConversation = useChatStore((state) => state.selectConversation);
  const createNewConversationUI = useChatStore((state) => state.createNewConversationUI);
  const clearHistory = useChatStore((state) => state.clearHistory);
  const toggleModelConfigPanel = useUIStore((state) => state.toggleModelConfigPanel);
  const loadConversations = useChatStore((state) => state.loadConversations);


  // 初次加载会话列表
  useEffect(() => {
    loadConversations();
  }, [loadConversations]); // 依赖 loadConversations action

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarSearch />
      </SidebarHeader>
      <SidebarContent className="flex flex-col h-full">
        <ConversationList
          conversations={conversations}
          onSelect={selectConversation} // 直接使用 store 的 action
          onClearHistory={clearHistory}   // 直接使用 store 的 action
          isLoading={isLoadingConversations}
        />
        <ToolsMenu
          onNewConversation={createNewConversationUI} // 直接使用 store 的 action
          onOpenModelConfig={toggleModelConfigPanel} // 直接使用 store 的 action
        />
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}