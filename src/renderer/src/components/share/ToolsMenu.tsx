import { cn } from "@renderer/lib/utils";
import { BookOpen, LayoutGrid, Plus, Settings } from "lucide-react";
import { Button } from "../ui/button";
import {
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from "../ui/sidebar";
import {ModeToggle} from "@renderer/components/share/ModeToggle"

interface ToolsMenuProps {
  onNewConversation: () => void;
  onOpenModelConfig?: () => void;
}

export function ToolsMenu({ onNewConversation, onOpenModelConfig }: ToolsMenuProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <>
      <SidebarGroup>
        <SidebarGroupLabel className={cn(
          "px-2 text-xs text-muted-foreground font-medium uppercase tracking-wide mb-2 mt-3",
          isCollapsed && "hidden"
        )}>
          工具
        </SidebarGroupLabel>

          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                className="w-full justify-start px-2.5 py-2 h-9 rounded-md text-sm"
                tooltip="知识库"
              >
                <BookOpen className="w-4 h-4 mr-2.5 opacity-70" />
                <span className="leading-tight font-medium">知识库</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton
                className="w-full justify-start px-2.5 py-2 h-9 rounded-md text-sm"
                tooltip="插件"
              >
                <LayoutGrid className="w-4 h-4 mr-2.5 opacity-70" />
                <span className="leading-tight font-medium">插件</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
      </SidebarGroup>

      <SidebarFooter className={cn(
        "border-t border-[#e5e5e5] dark:border-[#2A2A2A] p-4 mt-auto",
        isCollapsed && "flex flex-col items-center p-2"
      )}>
        <div className={cn(
          "mb-3",
          isCollapsed && "mb-2"
        )}>
          <Button
            className={cn(
              "bg-primary text-white hover:bg-primary/90 flex items-center justify-center font-medium rounded-lg",
              isCollapsed ? "w-8 h-8 p-0" : "w-full h-9"
            )}
            onClick={onNewConversation}
            title="新对话"
          >
            <Plus className="w-4 h-4" />
            {!isCollapsed && <span className="ml-2">新对话</span>}
          </Button>
        </div>
        <SidebarMenu className={cn(
          isCollapsed && "w-8"
        )}>
          <SidebarMenuItem>
            <ModeToggle />
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              className={cn(
                "h-9 rounded-md text-sm",
                isCollapsed ? "w-8 justify-center px-0" : "w-full justify-start px-2.5"
              )}
              tooltip="AI模型配置"
              onClick={onOpenModelConfig}
            >
              <Settings className="w-4 h-4 opacity-70" />
              {!isCollapsed && <span className="ml-2.5 leading-tight font-medium">AI模型配置</span>}
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </>
  );
}