import { cn } from "@renderer/lib/utils"

interface MessageBubbleProps {
  isUser: boolean;
  content: string;
  className?: string;
}

export function MessageBubble({ isUser, content, className }: MessageBubbleProps) {
  return (
    <div
      className={cn(
        `flex items-start my-5 ${isUser ? 'justify-end' : 'justify-start'}`,
        className
      )}
    >
      {!isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-accent flex items-center justify-center mr-3 text-xs font-medium shadow-sm">
          AI
        </div>
      )}
      <div
        className={cn(
          "max-w-[80%] px-4 py-3 rounded-xl shadow-sm",
          isUser
            ? "bg-[#1C1C1C] text-white rounded-br-none font-medium"
            : "bg-[#F0F0F0] text-[#1C1C1C] dark:bg-[#2A2A2A] dark:text-white rounded-bl-none font-medium"
        )}
      >
        <div className="leading-relaxed">{content}</div>
      </div>
      {isUser && (
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-[#2A2A2A] flex items-center justify-center ml-3 text-xs font-medium text-white shadow-sm">
          你
        </div>
      )}
    </div>
  )
}