import { Provider } from "@renderer/types"
import { BaseProvider } from "./base"
import DeepseekProvider from "./deepseek"
import GeminiProvider from "./gemini"
import { OpenAIProvider } from "./openai"

export default class ProviderFactory {
  static create(provider: Provider): BaseProvider {
    switch (provider.type) {
      case 'deepseek':
        return new DeepseekProvider(provider)
      case 'gemini':
        return new GeminiProvider(provider)
      default:
        return new OpenAIProvider(provider)
    }
  }
}