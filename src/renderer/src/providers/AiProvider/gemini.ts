import { google } from '@ai-sdk/google';
import { Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider } from './base';

export class GeminiProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  protected getDefaultModel(): string {
    return "gemini-1.5-pro-latest";
  }

  async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    try {
      const modelName = this.getModelName();
      const { text } = await generateText({
        model: google(modelName),
        messages: [
          { role: "system", content: prompt },
          { role: "user", content: content }
        ]
      });
      return text;
    } catch (error) {
      console.error('Gemini生成文本失败:', error);
      throw new Error(`Gemini生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    try {
      const modelName = this.getModelName();
      const { textStream } = streamText({
        model: google(modelName),
        messages: [
          { role: "system", content: prompt },
          { role: "user", content: content }
        ]
      });
      return textStream;
    } catch (error) {
      console.error('Gemini流式生成失败:', error);
      throw new Error(`Gemini流式生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

export default GeminiProvider;
