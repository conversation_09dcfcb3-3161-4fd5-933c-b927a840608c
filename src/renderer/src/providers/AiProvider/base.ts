import type { Provider} from '@renderer/types';
import { formatApiHost } from '@renderer/lib/urlUtil';

export abstract class BaseProvider {
  protected provider: Provider;
  protected host: string;
  protected apiKey: string;

  constructor(provider: Provider) {
    this.provider = provider;
    this.validateProvider();
    this.host = this.getBaseURL();
    this.apiKey = this.getApiKey();
  }

  abstract generateText({ prompt, content }: { prompt: string; content: string }): Promise<string>;
  abstract streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string>;

  protected validateProvider(): void {
    if (!this.provider) {
      throw new Error('提供者配置不能为空');
    }

    if (!this.provider.apiKey) {
      throw new Error(`${this.provider.name || this.provider.type} API密钥不能为空`);
    }
  }

  public getBaseURL(): string {
    const host = this.provider.apiHost;
    return formatApiHost(host);
  }

  public getApiKey(): string {
    const keys = this.provider.apiKey;
    if (!keys) {
      throw new Error(`${this.provider.name || this.provider.type} API密钥未配置`);
    }
    return keys;
  }

  protected getModelName(): string {
    // 如果provider.models中有启用的模型，则使用第一个
    if (this.provider.models && this.provider.models.length > 0) {
      return this.provider.models[0].id;
    }
    // 否则返回默认模型
    return this.getDefaultModel();
  }

  protected getDefaultModel(): string {
    // 子类可以覆盖此方法提供默认模型
    return '';
  }
}
