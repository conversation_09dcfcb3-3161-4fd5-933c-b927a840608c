import { deepseek } from '@ai-sdk/deepseek';
import { Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider } from './base';

export class DeepseekProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  protected getDefaultModel(): string {
    return "deepseek-chat";
  }

  async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    try {
      const modelName = this.getModelName();
      const { text } = await generateText({
        model: deepseek(modelName),
        messages: [
          { role: "system", content: prompt },
          { role: "user", content: content }
        ]
      });
      return text;
    } catch (error) {
      console.error('Deepseek生成文本失败:', error);
      throw new Error(`Deepseek生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    try {
      const modelName = this.getModelName();
      const { textStream } = streamText({
        model: deepseek(modelName),
        messages: [
          { role: "system", content: prompt },
          { role: "user", content: content }
        ]
      });
      return textStream;
    } catch (error) {
      console.error('Deepseek流式生成失败:', error);
      throw new Error(`Deepseek流式生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

export default DeepseekProvider;
