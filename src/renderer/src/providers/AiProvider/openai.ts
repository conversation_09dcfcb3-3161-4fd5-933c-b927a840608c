import { openai } from "@ai-sdk/openai";
import { Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider } from './base';

export class OpenAIProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  protected getDefaultModel(): string {
    return "gpt-3.5-turbo";
  }

  async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    try {
      const modelName = this.getModelName();
      const { text } = await generateText({
        model: openai(modelName),
        messages: [
          { role: "system", content: prompt },
          { role: "user", content: content }
        ]
      });
      return text;
    } catch (error) {
      console.error('OpenAI生成文本失败:', error);
      throw new Error(`OpenAI生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    try {
      const modelName = this.getModelName();
      const { textStream } = streamText({
        model: openai(modelName),
        messages: [
          { role: "system", content: prompt },
          { role: "user", content: content }
        ]
      });
      return textStream;
    } catch (error) {
      console.error('OpenAI流式生成失败:', error);
      throw new Error(`OpenAI流式生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

export default OpenAIProvider;
