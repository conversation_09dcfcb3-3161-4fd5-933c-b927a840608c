import { Provider } from '@renderer/types';
import { BaseProvider } from './base';
import ProviderFactory from './providerFactory';

export default class AiProvider {
  private sdk: BaseProvider;

  constructor(provider: Provider) {
    try {
      this.sdk = ProviderFactory.create(provider);
    } catch (error) {
      console.error('创建AI提供者实例失败:', error);
      throw new Error(`创建AI提供者失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  public async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    try {
      return await this.sdk.generateText({ prompt, content });
    } catch (error) {
      console.error('AI生成文本失败:', error);
      throw new Error(`AI生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  public streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    try {
      return this.sdk.streamText({ prompt, content });
    } catch (error) {
      console.error('AI流式生成失败:', error);
      throw new Error(`AI流式生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}
