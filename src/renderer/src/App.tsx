import { AppSidebar } from "@renderer/components/share/AppSidebar";
import { ChatArea } from "@renderer/components/share/ChatArea";
import ModelConfigPanel from "@renderer/components/share/ModelConfigPanel";
import { SidebarInset, SidebarProvider } from "@renderer/components/ui/sidebar";
import { useUIStore } from '@renderer/store/uiStore';
import { ThemeProvider } from './components/theme-provider';

function App() {

  const appIsModelConfigPanelOpen = useUIStore((state) => state.isModelConfigPanelOpen);


  return (
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <div className={`h-screen flex flex-col bg-background text-foreground`}>
        <div className="flex-1 flex overflow-hidden">
          <SidebarProvider>
            <AppSidebar />
            <SidebarInset>
              {appIsModelConfigPanelOpen ? (
                <ModelConfigPanel />
              ) : (
                <ChatArea />
              )}
            </SidebarInset>
          </SidebarProvider>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default App;
