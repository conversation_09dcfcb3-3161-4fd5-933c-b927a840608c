@import './base.css';

/* 全局基础样式 */
body {
  margin: 0;
  padding: 0;
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  height: 100vh;
  overflow: hidden;
}

code {
  font-weight: 600;
  padding: 3px 5px;
  border-radius: 2px;
  background-color: var(--color-background-mute);
  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  font-size: 85%;
}

/* Linear UI 风格相关样式 */
#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 增强的侧边栏过渡动画 */
aside {
  transition-property: width, opacity, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  will-change: width, opacity, transform;
}

main {
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-muted-foreground);
}

/* 自动聚焦相关样式 */
.focus-visible {
  outline: 2px solid var(--color-ring);
  outline-offset: 2px;
  transition: outline-color 0.2s ease;
}

/* 聊天气泡过渡效果 */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* Linear 风格的菜单栏 */
.menubar {
  display: flex;
  gap: 0.5rem;
}

.menubar-trigger {
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--color-foreground);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menubar-trigger:hover {
  background-color: var(--color-secondary);
}

.menubar-content {
  position: absolute;
  z-index: 50;
  min-width: 12rem;
  padding: 0.5rem;
  margin-top: 0.5rem;
  background-color: var(--color-popover);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.2s ease;
}

.menubar-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menubar-item:hover {
  background-color: var(--color-secondary);
}

.menubar-separator {
  height: 1px;
  margin: 0.5rem 0;
  background-color: var(--color-border);
}

.menubar-shortcut {
  margin-left: 1rem;
  color: var(--color-muted-foreground);
  font-size: 0.75rem;
}

/* Linear 风格的头像组件 */
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.avatar-image {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  font-weight: 500;
  border-radius: 50%;
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
}

/* 深色模式的特定样式 */
.dark {
  color-scheme: dark;
}

.dark .menubar-content {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* 聊天气泡样式 */
.message-bubble {
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  max-width: 80%;
  word-break: break-word;
}

.message-bubble-user {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border-top-right-radius: 0.25rem;
}

.message-bubble-ai {
  background-color: var(--color-secondary);
  color: var(--color-secondary-foreground);
  border-top-left-radius: 0.25rem;
}

/* 基础动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-0.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .menubar-content {
    min-width: 10rem;
  }
}
