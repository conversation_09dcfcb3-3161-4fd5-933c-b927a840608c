import { getAIConfig } from '@renderer/config/aiConfig';
import { formatTime } from '@renderer/lib/dateUtil';
import {
    streamMessageToAI as apiStreamMessageToAI,
    addMessage as dbAddMessage,
    createConversation as dbCreateConversation,
} from '@renderer/service/conversationService';
import { useChatStore, type Conversation, type Message } from '@renderer/store/chatStore';

/**
 * 聊天业务逻辑服务
 * 负责编排整个消息发送流程，将复杂的业务逻辑从 Store 中分离出来
 */
export const chatService = {
  /**
   * 发送消息的核心业务逻辑
   * @param text 用户输入的消息文本
   * @param currentModelId 当前选择的模型ID
   */
  sendMessage: async (text: string, currentModelId: number) => {
    const chatStore = useChatStore.getState();
    let conversationId = chatStore.selectedConversationId;

    try {
      // 1. 如果没有会话，创建新会话
      if (!conversationId) {
        conversationId = await chatService.createNewConversation(text);
      }

      const finalConversationId = conversationId as number;

      // 2. 添加用户消息到UI和数据库
      await chatService.addUserMessage(finalConversationId, text, currentModelId);

      // 3. 检查API Key配置
      const config = getAIConfig();
      if (!config.apiKey) {
        await chatService.handleNoApiKey(finalConversationId, currentModelId);
        return;
      }

      // 4. 准备接收AI回复：添加占位消息
      const tempAiMessageId = chatService.prepareAIResponse();

      // 5. 调用AI服务并流式处理回复
      await chatService.streamAIResponse(finalConversationId, text, currentModelId, tempAiMessageId);

      // 6. 刷新会话列表
      chatStore.loadConversations();

    } catch (error) {
      console.error("Error in chatService.sendMessage:", error);
      chatStore.handleSendError(error);
    }
  },

  /**
   * 创建新会话
   */
  createNewConversation: async (text: string): Promise<number> => {
    const chatStore = useChatStore.getState();

    const newTitle = text.slice(0, 30) + (text.length > 30 ? '...' : '');
    const conversationId = await dbCreateConversation(newTitle);

    const newConversation: Conversation = {
      id: conversationId,
      title: newTitle,
      time: formatTime(new Date().toISOString()),
      selected: true,
    };

    // 更新会话列表，并将新会话设为选中
    const { conversations } = chatStore;
    const updatedConversations = [
      newConversation,
      ...conversations.map(c => ({ ...c, selected: false }))
    ].sort((a, b) => b.id - a.id);

    useChatStore.setState({
      conversations: updatedConversations,
      selectedConversationId: conversationId,
      messages: [], // 新会话消息列表为空
    });

    return conversationId;
  },

  /**
   * 添加用户消息到UI和数据库
   */
  addUserMessage: async (conversationId: number, text: string, currentModelId: number): Promise<number> => {
    const chatStore = useChatStore.getState();

    // 添加用户消息到UI
    const tempUserMessageId = Date.now();
    const userMessage: Message = { id: tempUserMessageId, text, isUser: true };

    chatStore.addMessage(userMessage);
    chatStore.setInputText(''); // 清空输入框

    try {
      // 存储到数据库
      const userMessageDbId = await dbAddMessage(conversationId, 'user', text, currentModelId);

      // 用数据库ID更新UI消息
      chatStore.updateMessageId(tempUserMessageId, userMessageDbId);

      return userMessageDbId;
    } catch (error) {
      console.error('存储用户消息失败:', error);
      // 从UI移除发送失败的消息
      chatStore.removeMessage(tempUserMessageId);
      chatStore.setStreamingState(false);
      throw error;
    }
  },

  /**
   * 处理没有API Key的情况
   */
  handleNoApiKey: async (conversationId: number, currentModelId: number) => {
    const noApiKeyResponse = "请先在设置中配置 API密钥才能获取真实回复。";
    const tempAiMessageId = Date.now() + 1;

    const aiMessage: Message = { id: tempAiMessageId, text: noApiKeyResponse, isUser: false };
    useChatStore.getState().addMessage(aiMessage);

    try {
      const dbAiMsgId = await dbAddMessage(conversationId, 'assistant', noApiKeyResponse, currentModelId);
      useChatStore.getState().updateMessageId(tempAiMessageId, dbAiMsgId);
    } catch (e) {
      console.error("DB Error (no API key flow):", e);
    }

    useChatStore.getState().setStreamingState(false);
  },

  /**
   * 准备AI回复的占位消息
   */
  prepareAIResponse: (): number => {
    const tempAiMessageId = Date.now() + 1;
    const aiPlaceholderMessage: Message = { id: tempAiMessageId, text: '', isUser: false };

    const chatStore = useChatStore.getState();
    chatStore.addMessage(aiPlaceholderMessage);
    chatStore.setCurrentStreamingMessageId(tempAiMessageId);

    return tempAiMessageId;
  },

  /**
   * 流式处理AI回复
   */
  streamAIResponse: async (
    conversationId: number,
    text: string,
    currentModelId: number,
    tempAiMessageId: number
  ) => {
    const chatStore = useChatStore.getState();
    let fullResponse = '';

    try {
      await apiStreamMessageToAI(
        conversationId,
        text,
        (chunk) => {
          fullResponse += chunk;
          chatStore.updateStreamingMessage(tempAiMessageId, fullResponse);
        }
      );

      // 流式结束后，存储完整AI回复到数据库
      const finalAiMessageDbId = await dbAddMessage(conversationId, 'assistant', fullResponse, currentModelId);
      chatStore.updateMessageId(tempAiMessageId, finalAiMessageDbId);

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'AI响应失败';
      console.error('AI响应或存储失败:', error);

      // 更新UI显示错误消息
      chatStore.updateStreamingMessage(tempAiMessageId, errorMsg);

      try {
        const dbErrorMsgId = await dbAddMessage(conversationId, 'assistant', errorMsg, currentModelId);
        chatStore.updateMessageId(tempAiMessageId, dbErrorMsgId);
      } catch (e) {
        console.error("DB Error (AI error flow):", e);
      }
    } finally {
      // 重置流式状态
      chatStore.setStreamingState(false);
      chatStore.setCurrentStreamingMessageId(null);
    }
  },
};
