import { Model, Provider } from "@renderer/types";

// 获取所有已配置的 Provider 列表（包含其下的模型和模型设置）
export const getProviders = async (): Promise<Provider[]> => {
  try {
    // 使用新的preload API直接从主进程获取数据
    return await window.api.getAllProviders();
  } catch (error) {
    console.error('通过IPC获取Provider列表失败:', error);
    return [];
  }
};

// 获取单个 Provider 的详细信息
export const getProvider = async (id: string): Promise<Provider | undefined> => {
  try {
    // 使用新的preload API直接从主进程获取数据
    const result = await window.api.getProvider(id);
    return result || undefined;
  } catch (error) {
    console.error(`通过IPC获取Provider(ID: ${id})失败:`, error);
    return undefined;
  }
};

// 获取所有模型列表
export const getAllModel = async (): Promise<Model[] | undefined> => {
  try {
    // 使用新的preload API直接从主进程获取数据
    return await window.api.getAllModels();
  } catch (error) {
    console.error('通过IPC获取模型列表失败:', error);
    return [];
  }
}

// 获取特定模型的设置
export const getModelSetting = async (modelId: string): Promise<Model | undefined> => {
  try {
    // 使用新的preload API直接从主进程获取数据
    const result = await window.api.getModelSettings(modelId);
    return result || undefined;
  } catch (error) {
    console.error(`通过IPC获取模型设置(ModelID: ${modelId})失败:`, error);
    return undefined;
  }
};

// 保存 Provider 的配置
export const saveProvider = async (provider: Provider): Promise<void> => {
  try {
    // 使用新的preload API直接调用主进程
    await window.api.saveProvider(provider);
  } catch (error) {
    console.error('通过IPC保存Provider失败:', error);
    throw error;
  }
};

// 删除 Provider
export const deleteProvider = async (id: string): Promise<void> => {
  try {
    // 使用新的preload API直接调用主进程
    await window.api.deleteProvider(id);
  } catch (error) {
    console.error(`通过IPC删除Provider(ID: ${id})失败:`, error);
    throw error;
  }
};

// 保存模型设置
export const saveModelSetting = async (model: Model): Promise<void> => {
  try {
    // 使用新的preload API直接调用主进程
    await window.api.saveModelSettings(model);
  } catch (error) {
    console.error('通过IPC保存模型设置失败:', error);
    throw error;
  }
};

// 删除模型
export const deleteModel = async (modelId: string): Promise<void> => {
  try {
    // 使用新的preload API直接调用主进程
    await window.api.deleteModel(modelId);
  } catch (error) {
    console.error(`通过IPC删除模型(ID: ${modelId})失败:`, error);
    throw error;
  }
};
