import { Model, Provider } from '@renderer/types';

// 获取所有提供商，从providers读取
export const getAllProviders = async (): Promise<Provider[]> => {
  try {
    // 使用新的preload API直接从主进程获取数据
    return await window.api.getAllProviders();
  } catch (error) {
    console.error('通过IPC获取提供商失败:', error);
    throw error;
  }
};

// 获取提供商的所有模型
export const getModelsByProviderId = async (providerId: number): Promise<Model[]> => {
  try {
    // 使用新的preload API获取所有模型，然后过滤
    const allModels = await window.api.getAllModels();
    return allModels.filter(model => model.provider === providerId.toString());
  } catch (error) {
    console.error('通过IPC获取模型失败:', error);
    throw error;
  }
};

// 添加新提供商
export const addProvider = async (provider: Omit<Provider, 'id' | 'models'>): Promise<number> => {
  try {
    // 使用新的preload API直接调用主进程
    return await window.api.saveProvider({
      ...provider,
      id: 'new', // 标记为新Provider
      models: [] // 空模型列表
    });
  } catch (error) {
    console.error('通过IPC添加提供商失败:', error);
    throw error;
  }
};
