import { electronApp, is, optimizer } from '@electron-toolkit/utils'
import { app, BrowserWindow, ipcMain, shell } from 'electron'
import { join } from 'path'
import icon from '../../resources/icon.png?asset'
import { addMessageToDb, clearAllHistoryFromDb, createConversationInDb, deleteModelFromDb, deleteProviderFromDb, getAllModelsFromDb, getConversationsFromDb, getMessagesByConversationFromDb, getModelSettingsFromDb, getProviderFromDb, getProvidersFromDb, initializeDatabase, runMigrations, saveModelSettingsToDb, saveProviderToDb, seedDatabaseIfEmpty } from './db'

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  // 旧的通用数据库接口已移除，统一使用面向业务的IPC接口

  // 注册新的面向业务的IPC接口
  ipcMain.handle('providers:getAll', async () => {
    try {
      return await getProvidersFromDb();
    } catch (error) {
      console.error('获取Provider列表失败:', error);
      return [];
    }
  });

  ipcMain.handle('providers:getOne', async (_, id: string) => {
    try {
      return await getProviderFromDb(id);
    } catch (error) {
      console.error('获取Provider失败:', error);
      return null;
    }
  });

  ipcMain.handle('providers:save', async (_, providerData: any) => {
    try {
      return await saveProviderToDb(providerData);
    } catch (error) {
      console.error('保存Provider失败:', error);
      throw error;
    }
  });

  ipcMain.handle('providers:delete', async (_, id: string) => {
    try {
      return await deleteProviderFromDb(id);
    } catch (error) {
      console.error('删除Provider失败:', error);
      throw error;
    }
  });

  // Model相关IPC接口
  ipcMain.handle('models:getAll', async () => {
    try {
      return await getAllModelsFromDb();
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  });

  ipcMain.handle('models:getSettings', async (_, modelId: string) => {
    try {
      return await getModelSettingsFromDb(modelId);
    } catch (error) {
      console.error('获取模型设置失败:', error);
      return null;
    }
  });

  ipcMain.handle('models:saveSettings', async (_, modelData: any) => {
    try {
      return await saveModelSettingsToDb(modelData);
    } catch (error) {
      console.error('保存模型设置失败:', error);
      throw error;
    }
  });

  ipcMain.handle('models:delete', async (_, modelId: string) => {
    try {
      return await deleteModelFromDb(modelId);
    } catch (error) {
      console.error('删除模型失败:', error);
      throw error;
    }
  });

  // Conversation相关IPC接口
  ipcMain.handle('conversations:getAll', async () => {
    try {
      return await getConversationsFromDb();
    } catch (error) {
      console.error('获取对话列表失败:', error);
      return [];
    }
  });

  ipcMain.handle('conversations:create', async (_, title: string) => {
    try {
      return await createConversationInDb(title);
    } catch (error) {
      console.error('创建对话失败:', error);
      throw error;
    }
  });

  ipcMain.handle('messages:add', async (_, conversationId: number, role: string, content: string, modelId: number) => {
    try {
      return await addMessageToDb(conversationId, role, content, modelId);
    } catch (error) {
      console.error('添加消息失败:', error);
      throw error;
    }
  });

  ipcMain.handle('messages:getByConversation', async (_, conversationId: number) => {
    try {
      return await getMessagesByConversationFromDb(conversationId);
    } catch (error) {
      console.error('获取对话消息失败:', error);
      return [];
    }
  });

  ipcMain.handle('conversations:clearAll', async () => {
    try {
      return await clearAllHistoryFromDb();
    } catch (error) {
      console.error('清空历史记录失败:', error);
      throw error;
    }
  });

  // 初始化数据库
  try {
    initializeDatabase()
    await runMigrations()
    await seedDatabaseIfEmpty()
    console.log('数据库初始化与种子数据完成')
  } catch (error) {
    console.error('数据库初始化失败:', error)
  }

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
