// 种子数据：默认的供应商和模型配置
// 这些数据将在应用首次启动时写入数据库

// 定义供应商类型
export type ProviderType = 'openai' | 'anthropic' | 'gemini' | 'qwenlm' | 'azure-openai' | 'deepseek';

// 定义模型类型
export type ModelType = 'text' | 'vision' | 'embedding' | 'reasoning' | 'function_calling' | 'web_search';

// 定义模型接口
export interface SeedModel {
  id: string;
  name: string;
  group: string;
  description?: string;
  type?: ModelType[];
  // 数据库相关字段
  inputTokenLimit: number;
  outputTokenLimit: number;
  contextWindow: number;
  inputTokenPrice: number;
  outputTokenPrice: number;
}

// 定义供应商接口
export interface SeedProvider {
  type: ProviderType;
  name: string;
  apiHost: string;
  description: string;
  color: string;
  iconText: string;
  models: SeedModel[];
  enabled: boolean;
}

// 默认的供应商配置
export const SEED_PROVIDERS: SeedProvider[] = [
  {
    type: 'openai',
    name: 'OpenAI',
    apiHost: 'https://api.openai.com/v1',
    description: '支持GPT-4o、GPT-4、o1等模型',
    color: 'bg-green-500',
    iconText: 'AI',
    enabled: false,
    models: [
      {
        id: 'gpt-4.5-preview',
        name: 'GPT-4.5 Preview',
        group: 'GPT-4.5',
        inputTokenLimit: 128000,
        outputTokenLimit: 4096,
        contextWindow: 128000,
        inputTokenPrice: 0.01,
        outputTokenPrice: 0.03,
      },
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        group: 'GPT-4o',
        inputTokenLimit: 128000,
        outputTokenLimit: 4096,
        contextWindow: 128000,
        inputTokenPrice: 0.005,
        outputTokenPrice: 0.015,
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        group: 'GPT-4o',
        inputTokenLimit: 128000,
        outputTokenLimit: 16384,
        contextWindow: 128000,
        inputTokenPrice: 0.00015,
        outputTokenPrice: 0.0006,
      },
      {
        id: 'o1-mini',
        name: 'o1-mini',
        group: 'o1',
        inputTokenLimit: 128000,
        outputTokenLimit: 65536,
        contextWindow: 128000,
        inputTokenPrice: 0.003,
        outputTokenPrice: 0.012,
      },
      {
        id: 'o1-preview',
        name: 'o1-preview',
        group: 'o1',
        inputTokenLimit: 128000,
        outputTokenLimit: 32768,
        contextWindow: 128000,
        inputTokenPrice: 0.015,
        outputTokenPrice: 0.06,
      }
    ]
  },
  {
    type: 'anthropic',
    name: 'Anthropic',
    apiHost: 'https://api.anthropic.com',
    description: '支持Claude 3.5 Sonnet、Claude 3系列模型',
    color: 'bg-purple-500',
    iconText: 'AN',
    enabled: false,
    models: [
      {
        id: 'claude-3-7-sonnet-20250219',
        name: 'Claude 3.7 Sonnet',
        group: 'Claude 3.7',
        inputTokenLimit: 200000,
        outputTokenLimit: 8192,
        contextWindow: 200000,
        inputTokenPrice: 0.003,
        outputTokenPrice: 0.015,
      },
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        group: 'Claude 3.5',
        inputTokenLimit: 200000,
        outputTokenLimit: 8192,
        contextWindow: 200000,
        inputTokenPrice: 0.003,
        outputTokenPrice: 0.015,
      },
      {
        id: 'claude-3-5-haiku-20241022',
        name: 'Claude 3.5 Haiku',
        group: 'Claude 3.5',
        inputTokenLimit: 200000,
        outputTokenLimit: 8192,
        contextWindow: 200000,
        inputTokenPrice: 0.0008,
        outputTokenPrice: 0.004,
      },
      {
        id: 'claude-3-5-sonnet-20240620',
        name: 'Claude 3.5 Sonnet (Legacy)',
        group: 'Claude 3.5',
        inputTokenLimit: 200000,
        outputTokenLimit: 8192,
        contextWindow: 200000,
        inputTokenPrice: 0.003,
        outputTokenPrice: 0.015,
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        group: 'Claude 3',
        inputTokenLimit: 200000,
        outputTokenLimit: 4096,
        contextWindow: 200000,
        inputTokenPrice: 0.015,
        outputTokenPrice: 0.075,
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        group: 'Claude 3',
        inputTokenLimit: 200000,
        outputTokenLimit: 4096,
        contextWindow: 200000,
        inputTokenPrice: 0.00025,
        outputTokenPrice: 0.00125,
      }
    ]
  },
  {
    type: 'gemini',
    name: 'Google Gemini',
    apiHost: 'https://generativelanguage.googleapis.com',
    description: '支持Gemini 1.5和2.0系列模型',
    color: 'bg-blue-500',
    iconText: 'GM',
    enabled: false,
    models: [
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        group: 'Gemini 1.5',
        inputTokenLimit: 1000000,
        outputTokenLimit: 8192,
        contextWindow: 1000000,
        inputTokenPrice: 0.000075,
        outputTokenPrice: 0.0003,
      },
      {
        id: 'gemini-1.5-flash-8b',
        name: 'Gemini 1.5 Flash (8B)',
        group: 'Gemini 1.5',
        inputTokenLimit: 1000000,
        outputTokenLimit: 8192,
        contextWindow: 1000000,
        inputTokenPrice: 0.0000375,
        outputTokenPrice: 0.00015,
      },
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        group: 'Gemini 1.5',
        inputTokenLimit: 2000000,
        outputTokenLimit: 8192,
        contextWindow: 2000000,
        inputTokenPrice: 0.00125,
        outputTokenPrice: 0.005,
      },
      {
        id: 'gemini-2.0-flash',
        name: 'Gemini 2.0 Flash',
        group: 'Gemini 2.0',
        inputTokenLimit: 1000000,
        outputTokenLimit: 8192,
        contextWindow: 1000000,
        inputTokenPrice: 0.000075,
        outputTokenPrice: 0.0003,
      }
    ]
  },
  {
    type: 'deepseek',
    name: 'DeepSeek',
    apiHost: 'https://api.deepseek.com',
    description: '支持DeepSeek系列模型',
    color: 'bg-indigo-500',
    iconText: 'DS',
    enabled: false,
    models: [
      {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        group: 'DeepSeek',
        inputTokenLimit: 32768,
        outputTokenLimit: 4096,
        contextWindow: 32768,
        inputTokenPrice: 0.00014,
        outputTokenPrice: 0.00028,
      },
      {
        id: 'deepseek-coder',
        name: 'DeepSeek Coder',
        group: 'DeepSeek',
        inputTokenLimit: 16384,
        outputTokenLimit: 4096,
        contextWindow: 16384,
        inputTokenPrice: 0.00014,
        outputTokenPrice: 0.00028,
      }
    ]
  },
  {
    type: 'qwenlm',
    name: 'Qwen',
    apiHost: 'https://dashscope.aliyuncs.com/api/v1',
    description: '支持通义千问系列模型',
    color: 'bg-yellow-500',
    iconText: 'QW',
    enabled: false,
    models: [
      {
        id: 'qwen-turbo',
        name: 'Qwen Turbo',
        group: 'Qwen',
        inputTokenLimit: 6000,
        outputTokenLimit: 1500,
        contextWindow: 6000,
        inputTokenPrice: 0.0003,
        outputTokenPrice: 0.0006,
      },
      {
        id: 'qwen-plus',
        name: 'Qwen Plus',
        group: 'Qwen',
        inputTokenLimit: 30000,
        outputTokenLimit: 2000,
        contextWindow: 30000,
        inputTokenPrice: 0.0008,
        outputTokenPrice: 0.002,
      }
    ]
  },
  {
    type: 'azure-openai',
    name: 'Azure OpenAI',
    apiHost: 'https://YOUR_RESOURCE_NAME.openai.azure.com',
    description: '支持Azure部署的OpenAI模型',
    color: 'bg-blue-400',
    iconText: 'AZ',
    enabled: false,
    models: [
      {
        id: 'gpt-4o',
        name: 'GPT-4o (Azure)',
        group: 'GPT-4o',
        inputTokenLimit: 128000,
        outputTokenLimit: 4096,
        contextWindow: 128000,
        inputTokenPrice: 0.005,
        outputTokenPrice: 0.015,
      },
      {
        id: 'gpt-4',
        name: 'GPT-4 (Azure)',
        group: 'GPT-4',
        inputTokenLimit: 8192,
        outputTokenLimit: 4096,
        contextWindow: 8192,
        inputTokenPrice: 0.03,
        outputTokenPrice: 0.06,
      }
    ]
  }
];
