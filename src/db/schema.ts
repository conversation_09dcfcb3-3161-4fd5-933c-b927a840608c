import { int, real, sqliteTable, text } from 'drizzle-orm/sqlite-core';

// 定义聊天会话表
export const conversations = sqliteTable('conversations', {
  id: int('id').primaryKey(),
  title: text('title').notNull().default(''),
  createdAt: text('created_at').notNull()
});

// 定义聊天消息表
export const messages = sqliteTable('messages', {
  id: int('id').primaryKey(),
  conversationId: int('conversation_id').notNull(),
  role: text('role').notNull(),
  content: text('content').notNull(),
  modelId: int('model_id').notNull(),
  createdAt: text('created_at').notNull()
});

// 定义Providers表
export const providers = sqliteTable('providers', {
  id: int('id').primaryKey(),
  name: text('name').notNull(),
  apiKey: text('api_key').notNull(),
  apiHost: text('api_host').notNull(),
  enabled: int('enabled').default(1).notNull(),
  createdAt: text('created_at').notNull()
})

// 定义Models表
export const models = sqliteTable('models', {
  id: int('id').primaryKey(),
  providerId: int('provider_id').notNull(),
  name: text('name').notNull(),
  description: text('description'),
  inputTokenLimit: int('input_token_limit').notNull(),
  outputTokenLimit: int('output_token_limit').notNull(),
  contextWindow: int('context_window').notNull(),
  inputTokenPrice: real('input_token_price').notNull(),
  outputTokenPrice: real('output_token_price').notNull(),
  createdAt: text('created_at').notNull()
})

// 定义ModelsSetting表
export const models_setting = sqliteTable('models_setting', {
  id: int('id').primaryKey(),
  modelId: int('model_id').notNull(),
  temperature: real('temperature').default(0.7),
  topP: real('top_p').default(1.0),
  maxToken: int('max_tokens'),
  contextCount: int('context_count'),
  streamOutput: int('stream_output').default(1),
  createdAt: text('created_at').notNull()
})
